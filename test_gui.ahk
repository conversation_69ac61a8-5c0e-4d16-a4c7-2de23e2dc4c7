#Requires AutoHotkey v2.0

; 简单的GUI测试脚本
; 用于验证基本GUI功能是否正常

; 创建测试GUI
TestGui := Gui("+Resize", "GUI测试")

; 添加标题
TestGui.Add("Text", "x10 y10 w300 h30 Center", "AutoHotkey v2 GUI测试")

; 添加分隔线
TestGui.Add("Text", "x10 y40 w300 h2 0x10")

; 添加状态显示
TestGui.Add("Text", "x10 y50 w100 h20", "测试状态:")
StatusText := TestGui.Add("Text", "x120 y50 w100 h20 cGreen", "[正常]")

; 添加按钮
TestBtn := TestGui.Add("Button", "x10 y80 w100 h30", "测试按钮")
TestBtn.OnEvent("Click", TestButtonClick)

ExitBtn := TestGui.Add("Button", "x120 y80 w100 h30", "退出")
ExitBtn.OnEvent("Click", (*) => ExitApp())

; 添加状态栏
StatusBar := TestGui.Add("StatusBar", "", "GUI测试程序已启动")

; 显示GUI
TestGui.Show("w320 h150")

; 按钮点击事件
TestButtonClick(*) {
    StatusText.Text := "[已点击]"
    StatusText.Opt("+cBlue")
    StatusBar.SetText("按钮已被点击")
    MsgBox("测试按钮被点击！", "信息", "Icon!")
}
