@echo off
chcp 65001 >nul
title 游戏宏控制面板启动器

echo ========================================
echo           游戏宏控制面板
echo ========================================
echo.
echo 正在启动程序...
echo.

REM 检查AutoHotkey是否安装
where autohotkey >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] 未找到AutoHotkey程序
    echo 请确保已安装AutoHotkey v2.0或更高版本
    echo.
    pause
    exit /b 1
)

REM 检查主程序文件是否存在
if not exist "main.ahk" (
    echo [错误] 未找到主程序文件 main.ahk
    echo 请确保在正确的目录中运行此脚本
    echo.
    pause
    exit /b 1
)

REM 启动主程序
echo [信息] 启动游戏宏控制面板...
start "" "main.ahk"

REM 等待一下确保程序启动
timeout /t 2 /nobreak >nul

echo [成功] 程序已启动
echo.
echo 如果程序未正常显示，请检查：
echo 1. AutoHotkey版本是否为v2.0或更高
echo 2. 是否有杀毒软件阻止程序运行
echo 3. 程序文件是否完整
echo.
pause
