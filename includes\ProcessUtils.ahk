#Requires AutoHotkey v2.0

; 进程工具类 - 提供进程检测和窗口操作功能
; 包含游戏进程检测、窗口信息获取等实用函数

; 检测指定进程是否存在
ProcessExists(ProcessName) {
    local ProcessFound := false
    
    try {
        ; 使用WMI查询进程
        for Process in ComObjGet("winmgmts:").ExecQuery("SELECT * FROM Win32_Process WHERE Name='" . ProcessName . "'") {
            ProcessFound := true
            break
        }
    } catch Error as e {
        ; 如果WMI查询失败，使用ProcessExist函数
        ProcessFound := ProcessExist(ProcessName) ? true : false
    }
    
    return ProcessFound
}

; 获取进程窗口的客户区尺寸
GetProcessWindowSize(ProcessName) {
    local GameWindow := WinExist("ahk_exe " . ProcessName)
    local Width := 0
    local Height := 0
    
    if (GameWindow) {
        try {
            ; 获取客户区矩形
            local Rect := Buffer(16)
            if (DllCall("GetClientRect", "Ptr", GameWindow, "Ptr", Rect)) {
                Width := NumGet(Rect, 8, "Int")
                Height := NumGet(Rect, 12, "Int")
            }
        } catch Error as e {
            ; 获取失败时返回0
            Width := 0
            Height := 0
        }
    }
    
    return {Width: Width, Height: Height}
}

; 获取进程窗口标题
GetProcessWindowTitle(ProcessName) {
    local GameWindow := WinExist("ahk_exe " . ProcessName)
    local Title := ""
    
    if (GameWindow) {
        try {
            Title := WinGetTitle(GameWindow)
        } catch Error as e {
            Title := "获取失败"
        }
    }
    
    return Title
}

; 检查进程窗口是否激活
IsProcessWindowActive(ProcessName) {
    local GameWindow := WinExist("ahk_exe " . ProcessName)
    local ActiveWindow := WinExist("A")
    
    return (GameWindow && GameWindow = ActiveWindow)
}

; 激活进程窗口
ActivateProcessWindow(ProcessName) {
    local GameWindow := WinExist("ahk_exe " . ProcessName)
    local Success := false
    
    if (GameWindow) {
        try {
            WinActivate(GameWindow)
            Success := true
        } catch Error as e {
            Success := false
        }
    }
    
    return Success
}

; 获取进程详细信息
GetProcessInfo(ProcessName) {
    local Info := {
        Exists: false,
        PID: 0,
        WindowTitle: "",
        WindowSize: {Width: 0, Height: 0},
        IsActive: false
    }
    
    ; 检查进程是否存在
    Info.Exists := ProcessExists(ProcessName)
    
    if (Info.Exists) {
        ; 获取进程ID
        Info.PID := ProcessExist(ProcessName)
        
        ; 获取窗口信息
        Info.WindowTitle := GetProcessWindowTitle(ProcessName)
        Info.WindowSize := GetProcessWindowSize(ProcessName)
        Info.IsActive := IsProcessWindowActive(ProcessName)
    }
    
    return Info
}
