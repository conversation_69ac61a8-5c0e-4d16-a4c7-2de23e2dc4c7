#Requires AutoHotkey v2.0

; 游戏宏控制面板主程序
; 作者: AutoHotkey v2 脚本
; 功能: 提供游戏宏控制的图形界面

; 引入依赖模块
#Include includes\ProcessUtils.ahk
#Include includes\GuiUtils.ahk
#Include includes\ConfigManager.ahk
#Include plugins\MacroEngine.ahk

; 全局变量声明
global MainGui := ""
global ProcessStatus := false  ; 进程连接状态
global MacroStatus := false    ; 宏运行状态
global LuaStatus := false      ; Lua引擎状态
global GameProcess := "nhsm.exe"  ; 目标游戏进程
global WindowResolution := "未检测"  ; 窗口分辨率

; 状态指示器控件引用
global ProcessIndicator := ""
global MacroIndicator := ""
global LuaIndicator := ""
global ResolutionText := ""

; 按钮控件引用
global StartBtn := ""
global StopBtn := ""
global RebindBtn := ""
global SettingsBtn := ""
global LogBtn := ""
global ExitBtn := ""

; 宏引擎实例
global MacroEngineInstance := ""

; 配置管理器实例
global Config := ""

; 程序入口点
Main()

; 主函数 - 初始化GUI界面
Main() {
    ; 初始化配置管理器
    Config := ConfigManager("config.ini")

    ; 从配置文件读取游戏进程名
    GameProcess := Config.GetValue("游戏设置", "GameProcess", "nhsm.exe")

    ; 初始化宏引擎
    MacroEngineInstance := MacroEngine(GameProcess)

    ; 创建主窗口
    CreateMainGUI()

    ; 初始化状态检测
    InitializeStatus()

    ; 获取窗口尺寸配置
    local WinWidth := Config.GetInt("界面设置", "WindowWidth", 400)
    local WinHeight := Config.GetInt("界面设置", "WindowHeight", 300)

    ; 显示界面
    MainGui.Show("w" . WinWidth . " h" . WinHeight, "游戏宏控制面板")
}

; 创建主GUI界面
CreateMainGUI() {
    ; 创建主窗口
    MainGui := Gui("+Resize -MaximizeBox", "游戏宏控制面板")
    MainGui.OnEvent("Close", ExitProgram)
    
    ; 标题区域
    MainGui.Add("Text", "x10 y10 w380 h30 Center Section", "游戏宏控制面板")
    MainGui.Add("Text", "x10 y40 w380 h2 0x10")  ; 分隔线
    
    ; 游戏进程状态区域
    MainGui.Add("Text", "x10 y50 w100 h20", "游戏进程:")
    MainGui.Add("Text", "x110 y50 w100 h20", "[" . GameProcess . "]")
    ProcessIndicator := MainGui.Add("Text", "x220 y50 w80 h20", "[未连接]")
    
    ; 窗口分辨率显示
    MainGui.Add("Text", "x10 y75 w100 h20", "窗口分辨率:")
    ResolutionText := MainGui.Add("Text", "x110 y75 w200 h20", "[" . WindowResolution . "]")
    
    ; 分隔线
    MainGui.Add("Text", "x10 y100 w380 h2 0x10")
    
    ; 宏状态区域
    MainGui.Add("Text", "x10 y110 w80 h20", "宏状态:")
    MacroIndicator := MainGui.Add("Text", "x90 y110 w80 h20", "[●停止中]")
    
    ; Lua引擎状态
    MainGui.Add("Text", "x200 y110 w80 h20", "Lua引擎:")
    LuaIndicator := MainGui.Add("Text", "x280 y110 w80 h20", "[●未加载]")
    
    ; 分隔线
    MainGui.Add("Text", "x10 y135 w380 h2 0x10")
    
    ; 控制按钮区域 - 第一行
    StartBtn := MainGui.Add("Button", "x10 y150 w110 h35", "启动宏")
    StartBtn.OnEvent("Click", StartMacro)
    
    StopBtn := MainGui.Add("Button", "x130 y150 w110 h35", "停止宏")
    StopBtn.OnEvent("Click", StopMacro)
    
    RebindBtn := MainGui.Add("Button", "x250 y150 w130 h35", "重新绑定进程")
    RebindBtn.OnEvent("Click", RebindProcess)
    
    ; 控制按钮区域 - 第二行
    SettingsBtn := MainGui.Add("Button", "x10 y195 w110 h35", "设置")
    SettingsBtn.OnEvent("Click", OpenSettings)
    
    LogBtn := MainGui.Add("Button", "x130 y195 w110 h35", "日志")
    LogBtn.OnEvent("Click", OpenLog)
    
    ExitBtn := MainGui.Add("Button", "x250 y195 w130 h35", "退出程序")
    ExitBtn.OnEvent("Click", ExitProgram)
    
    ; 状态栏
    local StatusBar := MainGui.Add("StatusBar", "", "就绪")
    StatusBar.SetText("游戏宏控制面板已启动")
}

; 初始化状态检测
InitializeStatus() {
    ; 检测游戏进程
    CheckGameProcess()

    ; 检测Lua引擎状态
    CheckLuaEngine()

    ; 从配置文件读取状态更新间隔
    local UpdateInterval := Config.GetInt("界面设置", "StatusUpdateInterval", 2000)

    ; 设置定时器定期更新状态
    SetTimer(UpdateStatus, UpdateInterval)
}

; 检测游戏进程状态
CheckGameProcess() {
    ; 使用ProcessUtils模块检测进程
    local ProcessFound := ProcessExists(GameProcess)

    ; 更新进程状态
    ProcessStatus := ProcessFound
    if (ProcessFound) {
        UpdateStatusIndicator(ProcessIndicator, true, "[已连接✓]", "[未连接]")

        ; 获取窗口分辨率
        GetWindowResolution()
    } else {
        UpdateStatusIndicator(ProcessIndicator, false, "[已连接✓]", "[未连接]")
        WindowResolution := "未检测"
        ResolutionText.Text := "[" . WindowResolution . "]"
    }
}

; 获取游戏窗口分辨率
GetWindowResolution() {
    ; 使用ProcessUtils模块获取窗口尺寸
    local WindowSize := GetProcessWindowSize(GameProcess)

    if (WindowSize.Width > 0 && WindowSize.Height > 0) {
        WindowResolution := WindowSize.Width . "x" . WindowSize.Height
        ResolutionText.Text := "[" . WindowResolution . "]"
    } else {
        WindowResolution := "未检测"
        ResolutionText.Text := "[" . WindowResolution . "]"
    }
}

; 检测Lua引擎状态
CheckLuaEngine() {
    ; 检查宏引擎中的Lua引擎状态
    if (MacroEngineInstance) {
        local Status := MacroEngineInstance.GetStatus()
        LuaStatus := Status.LuaEngineLoaded
    } else {
        LuaStatus := false
    }

    ; 更新状态指示器
    UpdateStatusIndicator(LuaIndicator, LuaStatus, "[●已加载]", "[●未加载]")
}

; 定期更新状态
UpdateStatus() {
    CheckGameProcess()
    CheckLuaEngine()
}

; 启动宏功能
StartMacro(*) {
    if (!ProcessStatus) {
        ShowErrorMessage("请先连接到游戏进程！")
        return
    }

    if (MacroEngineInstance) {
        local Result := MacroEngineInstance.Start()
        if (Result.Success) {
            MacroStatus := true
            UpdateStatusIndicator(MacroIndicator, true, "[●运行中]", "[●停止中]")
            ShowInfoMessage("宏已启动")
        } else {
            ShowErrorMessage("宏启动失败: " . Result.Message)
        }
    } else {
        ShowErrorMessage("宏引擎未初始化")
    }
}

; 停止宏功能
StopMacro(*) {
    if (MacroEngineInstance) {
        local Result := MacroEngineInstance.Stop()
        if (Result.Success) {
            MacroStatus := false
            UpdateStatusIndicator(MacroIndicator, false, "[●运行中]", "[●停止中]")
            ShowInfoMessage("宏已停止")
        } else {
            ShowErrorMessage("宏停止失败: " . Result.Message)
        }
    } else {
        MacroStatus := false
        UpdateStatusIndicator(MacroIndicator, false, "[●运行中]", "[●停止中]")
        ShowInfoMessage("宏已停止")
    }
}

; 重新绑定进程
RebindProcess(*) {
    CheckGameProcess()
    if (ProcessStatus) {
        ShowInfoMessage("成功重新绑定到游戏进程")
    } else {
        ShowErrorMessage("未找到游戏进程: " . GameProcess)
    }
}

; 打开设置界面
OpenSettings(*) {
    ShowInfoMessage("设置功能开发中...")
}

; 打开日志界面
OpenLog(*) {
    ShowInfoMessage("日志功能开发中...")
}

; 退出程序
ExitProgram(*) {
    if (ShowConfirmDialog("确定要退出程序吗？", "确认退出")) {
        ; 停止宏引擎
        if (MacroEngineInstance && MacroStatus) {
            MacroEngineInstance.Stop()
        }
        ExitApp()
    }
}
