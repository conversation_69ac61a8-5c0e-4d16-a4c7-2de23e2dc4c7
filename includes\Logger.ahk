#Requires AutoHotkey v2.0

; 日志管理器 - 提供日志记录功能
; 支持不同级别的日志记录和文件管理

class Logger {
    ; 日志级别常量
    static DEBUG := 0
    static INFO := 1
    static WARNING := 2
    static ERROR := 3
    
    ; 日志级别名称
    static LevelNames := ["DEBUG", "INFO", "WARNING", "ERROR"]
    
    ; 实例属性
    LogFile := ""
    LogLevel := 1  ; 默认INFO级别
    MaxFileSize := 10485760  ; 10MB
    EnableConsole := true
    
    ; 构造函数
    __New(LogFilePath := "logs\macro.log", Level := 1) {
        this.LogFile := LogFilePath
        this.LogLevel := Level
        
        ; 确保日志目录存在
        local LogDir := RegExReplace(LogFilePath, "\\[^\\]*$", "")
        if (LogDir != LogFilePath && !DirExist(LogDir)) {
            DirCreate(LogDir)
        }
    }
    
    ; 记录调试信息
    Debug(Message) {
        this.Log(Logger.DEBUG, Message)
    }
    
    ; 记录一般信息
    Info(Message) {
        this.Log(Logger.INFO, Message)
    }
    
    ; 记录警告信息
    Warning(Message) {
        this.Log(Logger.WARNING, Message)
    }
    
    ; 记录错误信息
    Error(Message) {
        this.Log(Logger.ERROR, Message)
    }
    
    ; 通用日志记录方法
    Log(Level, Message) {
        ; 检查日志级别
        if (Level < this.LogLevel) {
            return
        }
        
        ; 格式化日志消息
        local Timestamp := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss")
        local LevelName := Logger.LevelNames[Level + 1]
        local LogMessage := "[" . Timestamp . "] [" . LevelName . "] " . Message
        
        ; 输出到控制台（如果启用）
        if (this.EnableConsole) {
            OutputDebug(LogMessage)
        }
        
        ; 写入日志文件
        this.WriteToFile(LogMessage)
    }
    
    ; 写入日志文件
    WriteToFile(Message) {
        try {
            ; 检查文件大小，如果超过限制则轮转
            if (FileExist(this.LogFile)) {
                local FileSize := FileGetSize(this.LogFile)
                if (FileSize > this.MaxFileSize) {
                    this.RotateLogFile()
                }
            }
            
            ; 追加日志消息
            FileAppend(Message . "`n", this.LogFile, "UTF-8")
        } catch Error as e {
            ; 日志写入失败，输出到调试控制台
            OutputDebug("[Logger Error] Failed to write log: " . e.Message)
        }
    }
    
    ; 轮转日志文件
    RotateLogFile() {
        try {
            ; 生成备份文件名
            local BackupFile := RegExReplace(this.LogFile, "\.log$", "_" . FormatTime(A_Now, "yyyyMMdd_HHmmss") . ".log")
            
            ; 移动当前日志文件为备份
            FileMove(this.LogFile, BackupFile)
            
            ; 清理旧的备份文件（保留最近5个）
            this.CleanupOldLogs()
        } catch Error as e {
            OutputDebug("[Logger Error] Failed to rotate log file: " . e.Message)
        }
    }
    
    ; 清理旧的日志备份文件
    CleanupOldLogs() {
        try {
            local LogDir := RegExReplace(this.LogFile, "\\[^\\]*$", "")
            local LogName := RegExReplace(this.LogFile, "^.*\\", "")
            local Pattern := RegExReplace(LogName, "\.log$", "_*.log")
            
            local BackupFiles := []
            
            ; 收集所有备份文件
            Loop Files, LogDir . "\" . Pattern {
                BackupFiles.Push({Name: A_LoopFileName, Path: A_LoopFileFullPath, Time: A_LoopFileTimeModified})
            }
            
            ; 按修改时间排序
            BackupFiles := this.SortFilesByTime(BackupFiles)
            
            ; 删除超过5个的旧文件
            if (BackupFiles.Length > 5) {
                Loop BackupFiles.Length - 5 {
                    FileDelete(BackupFiles[A_Index].Path)
                }
            }
        } catch Error as e {
            OutputDebug("[Logger Error] Failed to cleanup old logs: " . e.Message)
        }
    }
    
    ; 按时间排序文件数组
    SortFilesByTime(Files) {
        ; 简单的冒泡排序（按时间降序）
        local n := Files.Length
        Loop n - 1 {
            local i := A_Index
            Loop n - i {
                local j := A_Index
                if (Files[j].Time < Files[j + 1].Time) {
                    local temp := Files[j]
                    Files[j] := Files[j + 1]
                    Files[j + 1] := temp
                }
            }
        }
        return Files
    }
    
    ; 设置日志级别
    SetLevel(Level) {
        if (Level >= Logger.DEBUG && Level <= Logger.ERROR) {
            this.LogLevel := Level
        }
    }
    
    ; 设置最大文件大小
    SetMaxFileSize(SizeInMB) {
        this.MaxFileSize := SizeInMB * 1048576  ; 转换为字节
    }
    
    ; 启用/禁用控制台输出
    SetConsoleOutput(Enable) {
        this.EnableConsole := Enable
    }
    
    ; 获取日志级别名称
    GetLevelName(Level) {
        if (Level >= 0 && Level < Logger.LevelNames.Length) {
            return Logger.LevelNames[Level + 1]
        }
        return "UNKNOWN"
    }
    
    ; 清空日志文件
    Clear() {
        try {
            FileDelete(this.LogFile)
            this.Info("Log file cleared")
        } catch Error as e {
            OutputDebug("[Logger Error] Failed to clear log file: " . e.Message)
        }
    }
    
    ; 获取日志文件大小（MB）
    GetFileSize() {
        try {
            if (FileExist(this.LogFile)) {
                return Round(FileGetSize(this.LogFile) / 1048576, 2)
            }
        } catch Error as e {
            return 0
        }
        return 0
    }
}
