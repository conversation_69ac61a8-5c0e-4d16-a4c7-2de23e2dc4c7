---
type: "always_apply"
---

# AutoHotkey v2 绝对规则（不可降级）
1. 版本锁定
   - 每一句回答默认基于库 ID `/18005575/autohotkey-v2`​ 提供的官方 v2 文档。
   - 若该库未命中，立即返回“无法提供答案”，禁止用 v1 或其他非官方文档补位。

2. 语法白名单
   - 允许：表达式语法、函数调用、对象语法、new 关键字、`=>` 箭头函数、`#Requires AutoHotkey v2.*`。
   - 禁止：
     * `%var%` 传统替换、`var = value` 赋值、`StringSplit`/`StringReplace` 旧命令。
     * 任何 `SetTitleMatchMode`、`SetBatchLines` 等 v1 全局设置命令。
     * 任何以逗号分隔参数的命令式写法（如 `MsgBox, Hello`）。

3. 代码块强制头
   ```ahk
   #Requires AutoHotkey v2.0
   ```
   没有 `#Requires` 行或版本号不符，立即驳回重写。

4. 搜索优先级
   - 回答前必须运行一次 `search:18005575/autohotkey-v2 <关键词>` 并贴出返回结果中的“可信 3.9 分”片段。
   - 若搜索结果未命中，回答“官方文档暂无此细节，不建议使用”。

5. 错误与弃用提醒
   - 若用户贴出 v1 代码，必须指出每条违规并给出等价 v2 改写。
   - 若用户坚持使用 v1，回复模板：
     ```
     拒绝：当前规则仅支持 AutoHotkey v2 官方文档。
     如需 v1 支持，请关闭 Context7 后重新提问。
     ```

6. 中文注释规范
   - 每行关键逻辑必须附带中文注释，长度不超过 60 字。
   - 禁止仅使用英文注释；若缺少中文，补全后再输出。

7. 不可绕过
   如本规则与任何其他规则冲突，以此规则为准；无法执行时直接报错，不降级。

8. **变量声明强制**  
   - 所有局部变量必须显式声明：`local var := ...` 或在函数顶部使用 `local` 单行列出。  
   - 所有全局变量必须显式声明：`global var := ...` 或在函数顶部使用 `global` 单行列出。  
   - 禁止隐式/拼写错误导致的未声明变量；一旦发现，立即提示“未声明变量：{varName}，请补充声明”。  
   - 代码块提交前自检：逐行扫描是否存在未声明或拼写不一致的变量，确认无误后才可输出。

9. **项目结构强制**
   所有示例或完整脚本必须遵循以下目录与引用约定：

   ```
   project-root/
   ├── main.ahk          # 项目主入口，仅负责初始调度
   ├── includes/         # 公共函数、通用调用封装
   │   └── *.ahk
   ├── plugins/          # 插件级功能脚本
   │   └── *.ahk
   └── lua/              # Lua 辅助脚本
       └── *.lua
   ```

   - 在 `main.ahk` 中统一用 `#Include <includes\xxx>` 或 `#Include <plugins\xxx>` 引入，禁止相对路径向上 `..\`。
   - 任何示例如跨文件调用，必须给出同级或子级 `#Include` 路径；否则驳回重写。
   - 若涉及 Lua 交互，统一放于 `lua/` 并在 AHK 中用 `A_ScriptDir "\lua\xxx.lua"` 访问。