# 游戏宏控制面板

基于AutoHotkey v2开发的游戏宏控制面板，提供图形化界面来管理和控制游戏宏功能。

## 功能特性

- 🎮 **游戏进程检测** - 自动检测并连接到目标游戏进程
- 🖥️ **窗口分辨率显示** - 实时显示游戏窗口分辨率
- ⚡ **宏引擎控制** - 启动/停止宏执行，支持实时状态监控
- 🔧 **Lua脚本支持** - 集成Lua引擎，支持复杂宏逻辑
- 📊 **状态指示器** - 直观显示各组件运行状态
- 🎛️ **图形化界面** - 简洁易用的控制面板

## 项目结构

```
nshmex/
├── main.ahk                 # 主程序入口
├── includes/                # 公共函数库
│   ├── ProcessUtils.ahk     # 进程检测工具
│   └── GuiUtils.ahk         # GUI界面工具
├── plugins/                 # 插件功能模块
│   └── MacroEngine.ahk      # 宏引擎核心
├── lua/                     # Lua脚本目录
│   └── macro_script.lua     # 宏脚本示例
└── README.md               # 项目说明文档
```

## 界面预览

```
┌─────────────────────────────────────┐
│           游戏宏控制面板             │
├─────────────────────────────────────┤
│ 游戏进程: [nhsm.exe] [已连接✓]      │ 
│ 窗口分辨率: [1920x1080]             │
├─────────────────────────────────────┤
│ 宏状态: [●停止中] [●运行中]          │
│ Lua引擎: [●已加载] [●未加载]        │
├─────────────────────────────────────┤
│ [启动宏] [停止宏] [重新绑定进程]     │
│ [设置]   [日志]   [退出程序]        │
└─────────────────────────────────────┘
```

## 安装要求

- Windows 10/11 操作系统
- AutoHotkey v2.0 或更高版本
- 目标游戏进程：nhsm.exe（可在代码中修改）

## 使用方法

### 1. 启动程序
```bash
# 双击运行主程序
main.ahk
```

### 2. 基本操作
1. **进程检测** - 程序会自动检测目标游戏进程
2. **启动宏** - 点击"启动宏"按钮开始执行宏功能
3. **停止宏** - 点击"停止宏"按钮停止宏执行
4. **重新绑定** - 如果进程连接丢失，点击"重新绑定进程"

### 3. 状态指示器说明
- **游戏进程** - 显示目标进程连接状态
- **窗口分辨率** - 显示游戏窗口的客户区尺寸
- **宏状态** - 显示宏引擎运行状态
- **Lua引擎** - 显示Lua脚本引擎加载状态

## 配置说明

### 修改目标进程
在 `main.ahk` 中修改以下变量：
```autohotkey
global GameProcess := "your_game.exe"  ; 替换为你的游戏进程名
```

### 自定义宏逻辑
编辑 `plugins/MacroEngine.ahk` 中的 `ExecuteMacroActions()` 函数：
```autohotkey
ExecuteMacroActions() {
    ; 在这里添加你的宏逻辑
    ; 例如：检测像素颜色、发送按键、鼠标点击等
}
```

### Lua脚本开发
在 `lua/` 目录下创建或修改Lua脚本：
```lua
-- 示例：自动点击红色按钮
function auto_click_red_button()
    local color = get_pixel_color(100, 100)
    if color == 0xFF0000 then
        send_click(100, 100)
    end
end
```

## 核心模块说明

### ProcessUtils.ahk
提供进程检测和窗口操作功能：
- `ProcessExists(ProcessName)` - 检测进程是否存在
- `GetProcessWindowSize(ProcessName)` - 获取窗口尺寸
- `GetProcessInfo(ProcessName)` - 获取进程详细信息

### GuiUtils.ahk
提供GUI界面相关工具函数：
- `UpdateStatusIndicator()` - 更新状态指示器
- `ShowInfoMessage()` - 显示信息消息
- `ShowErrorMessage()` - 显示错误消息

### MacroEngine.ahk
宏引擎核心功能：
- `Start()` - 启动宏引擎
- `Stop()` - 停止宏引擎
- `ExecuteMacroActions()` - 执行宏逻辑
- `GetStatus()` - 获取引擎状态

## 开发指南

### 添加新功能
1. 在相应的模块文件中添加函数
2. 在主程序中调用新功能
3. 更新GUI界面（如需要）

### 调试技巧
- 使用 `MsgBox()` 输出调试信息
- 检查AutoHotkey控制台输出
- 使用Windows任务管理器监控进程状态

## 注意事项

⚠️ **重要提醒**
- 本工具仅供学习和研究使用
- 请遵守游戏服务条款，避免违规使用
- 使用前请备份重要数据
- 建议在测试环境中先行验证功能

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目！

## 更新日志

### v1.0.0 (2025-08-02)
- ✨ 初始版本发布
- 🎮 支持游戏进程检测
- ⚡ 基础宏引擎功能
- 🖥️ 图形化控制界面
- 🔧 Lua脚本支持框架
