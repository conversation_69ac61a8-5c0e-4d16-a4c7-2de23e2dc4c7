[游戏设置]
; 目标游戏进程名（不包含路径）
GameProcess=nhsm.exe

; 游戏窗口标题（可选，用于更精确的窗口识别）
GameWindowTitle=

[宏设置]
; 宏执行间隔（毫秒）
MacroInterval=50

; 是否在游戏窗口失去焦点时暂停宏
PauseWhenInactive=true

; 宏启动延迟（毫秒）
StartupDelay=1000

[界面设置]
; 主窗口宽度
WindowWidth=400

; 主窗口高度
WindowHeight=300

; 状态更新间隔（毫秒）
StatusUpdateInterval=2000

; 是否显示详细状态信息
ShowDetailedStatus=true

[Lua设置]
; Lua脚本文件路径
LuaScriptPath=lua\macro_script.lua

; 是否自动加载Lua引擎
AutoLoadLua=false

; Lua脚本执行间隔（毫秒）
LuaExecuteInterval=100

[安全设置]
; 是否启用安全模式（限制某些危险操作）
SafeMode=true

; 最大连续执行时间（分钟，0表示无限制）
MaxRunTime=0

; 是否在检测到异常时自动停止
AutoStopOnError=true

[日志设置]
; 是否启用日志记录
EnableLogging=true

; 日志文件路径
LogFilePath=logs\macro.log

; 日志级别（DEBUG, INFO, WARNING, ERROR）
LogLevel=INFO

; 最大日志文件大小（MB）
MaxLogSize=10

[热键设置]
; 启动/停止宏的热键（留空表示禁用）
ToggleMacroHotkey=F9

; 紧急停止热键
EmergencyStopHotkey=F12

; 显示/隐藏界面热键
ToggleGUIHotkey=F8

[高级设置]
; 是否启用多线程处理
EnableMultiThreading=false

; 内存使用限制（MB，0表示无限制）
MemoryLimit=0

; 是否启用性能监控
EnablePerformanceMonitor=false
