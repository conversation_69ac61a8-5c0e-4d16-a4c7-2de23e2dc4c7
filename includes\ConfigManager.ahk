#Requires AutoHotkey v2.0

; 配置管理器 - 负责读取和管理配置文件
; 提供配置文件的读取、写入和默认值管理

class ConfigManager {
    ; 配置文件路径
    ConfigFile := ""
    
    ; 配置数据缓存
    ConfigData := Map()
    
    ; 构造函数
    __New(ConfigFilePath := "config.ini") {
        this.ConfigFile := ConfigFilePath
        this.LoadConfig()
    }
    
    ; 加载配置文件
    LoadConfig() {
        if (!FileExist(this.ConfigFile)) {
            ; 如果配置文件不存在，创建默认配置
            this.CreateDefaultConfig()
            return
        }
        
        try {
            ; 读取配置文件内容
            local Content := FileRead(this.ConfigFile)
            local Lines := StrSplit(Content, "`n")
            local CurrentSection := ""
            
            ; 解析配置文件
            for Line in Lines {
                Line := Trim(Line)
                
                ; 跳过空行和注释
                if (Line = "" || SubStr(Line, 1, 1) = ";") {
                    continue
                }
                
                ; 检查是否是节标题
                if (SubStr(Line, 1, 1) = "[" && SubStr(Line, -1) = "]") {
                    CurrentSection := SubStr(Line, 2, -1)
                    if (!this.ConfigData.Has(CurrentSection)) {
                        this.ConfigData[CurrentSection] := Map()
                    }
                    continue
                }
                
                ; 解析键值对
                local EqualPos := InStr(Line, "=")
                if (EqualPos > 0 && CurrentSection != "") {
                    local Key := Trim(SubStr(Line, 1, EqualPos - 1))
                    local Value := Trim(SubStr(Line, EqualPos + 1))
                    this.ConfigData[CurrentSection][Key] := Value
                }
            }
        } catch Error as e {
            ; 配置文件读取失败，使用默认配置
            this.CreateDefaultConfig()
        }
    }
    
    ; 创建默认配置
    CreateDefaultConfig() {
        ; 游戏设置
        this.SetValue("游戏设置", "GameProcess", "nhsm.exe")
        this.SetValue("游戏设置", "GameWindowTitle", "")
        
        ; 宏设置
        this.SetValue("宏设置", "MacroInterval", "50")
        this.SetValue("宏设置", "PauseWhenInactive", "true")
        this.SetValue("宏设置", "StartupDelay", "1000")
        
        ; 界面设置
        this.SetValue("界面设置", "WindowWidth", "400")
        this.SetValue("界面设置", "WindowHeight", "300")
        this.SetValue("界面设置", "StatusUpdateInterval", "2000")
        this.SetValue("界面设置", "ShowDetailedStatus", "true")
        
        ; Lua设置
        this.SetValue("Lua设置", "LuaScriptPath", "lua\macro_script.lua")
        this.SetValue("Lua设置", "AutoLoadLua", "false")
        this.SetValue("Lua设置", "LuaExecuteInterval", "100")
        
        ; 安全设置
        this.SetValue("安全设置", "SafeMode", "true")
        this.SetValue("安全设置", "MaxRunTime", "0")
        this.SetValue("安全设置", "AutoStopOnError", "true")
        
        ; 保存默认配置到文件
        this.SaveConfig()
    }
    
    ; 获取配置值
    GetValue(Section, Key, DefaultValue := "") {
        if (this.ConfigData.Has(Section) && this.ConfigData[Section].Has(Key)) {
            return this.ConfigData[Section][Key]
        }
        return DefaultValue
    }
    
    ; 设置配置值
    SetValue(Section, Key, Value) {
        if (!this.ConfigData.Has(Section)) {
            this.ConfigData[Section] := Map()
        }
        this.ConfigData[Section][Key] := String(Value)
    }
    
    ; 获取布尔值
    GetBool(Section, Key, DefaultValue := false) {
        local Value := this.GetValue(Section, Key, DefaultValue ? "true" : "false")
        return (Value = "true" || Value = "1" || Value = "yes")
    }
    
    ; 获取整数值
    GetInt(Section, Key, DefaultValue := 0) {
        local Value := this.GetValue(Section, Key, String(DefaultValue))
        return Integer(Value)
    }
    
    ; 获取浮点数值
    GetFloat(Section, Key, DefaultValue := 0.0) {
        local Value := this.GetValue(Section, Key, String(DefaultValue))
        return Float(Value)
    }
    
    ; 保存配置到文件
    SaveConfig() {
        try {
            local Content := ""
            
            ; 遍历所有节
            for Section, SectionData in this.ConfigData {
                Content .= "[" . Section . "]`n"
                
                ; 遍历节中的所有键值对
                for Key, Value in SectionData {
                    Content .= Key . "=" . Value . "`n"
                }
                
                Content .= "`n"
            }
            
            ; 写入文件
            FileDelete(this.ConfigFile)
            FileAppend(Content, this.ConfigFile, "UTF-8")
            
            return true
        } catch Error as e {
            return false
        }
    }
    
    ; 重新加载配置
    ReloadConfig() {
        this.ConfigData := Map()
        this.LoadConfig()
    }
    
    ; 检查配置文件是否存在
    ConfigExists() {
        return FileExist(this.ConfigFile) ? true : false
    }
    
    ; 获取所有节名
    GetSections() {
        local Sections := []
        for Section in this.ConfigData {
            Sections.Push(Section)
        }
        return Sections
    }
    
    ; 获取指定节的所有键
    GetKeys(Section) {
        local Keys := []
        if (this.ConfigData.Has(Section)) {
            for Key in this.ConfigData[Section] {
                Keys.Push(Key)
            }
        }
        return Keys
    }
    
    ; 删除配置项
    DeleteValue(Section, Key) {
        if (this.ConfigData.Has(Section) && this.ConfigData[Section].Has(Key)) {
            this.ConfigData[Section].Delete(Key)
            return true
        }
        return false
    }
    
    ; 删除整个节
    DeleteSection(Section) {
        if (this.ConfigData.Has(Section)) {
            this.ConfigData.Delete(Section)
            return true
        }
        return false
    }
}
